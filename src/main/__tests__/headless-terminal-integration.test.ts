import { MCPService } from '../mcp-service'
import { MCPConfig } from '../../shared/types'

// Mock the config store
const mockConfigStore = {
  get: jest.fn()
}

jest.mock('../config', () => ({
  configStore: mockConfigStore
}))

describe('MCP Service Integration', () => {
  let mcpService: MCPService

  beforeEach(() => {
    mcpService = new MCPService()
    jest.clearAllMocks()
  })

  describe('Parameter Name Correction', () => {
    it('should fix snake_case to camelCase parameter naming', () => {
      const mcpServiceInstance = mcpService as any

      // Test the fixParameterNaming method
      const originalArgs = {
        session_id: "test-session-123",
        command: "ls ~/Desktop"
      }

      const fixedArgs = mcpServiceInstance.fixParameterNaming(originalArgs)

      expect(fixedArgs).toEqual({
        sessionId: "test-session-123",
        command: "ls ~/Desktop"
      })
      expect(fixedArgs).not.toHaveProperty('session_id')
    })

    it('should fix parameter naming based on error message', () => {
      const mcpServiceInstance = mcpService as any

      const originalArgs = {
        user_name: "john_doe",
        file_path: "/tmp/test.txt"
      }

      const errorMessage = "missing field `userName`"
      const fixedArgs = mcpServiceInstance.fixParameterNaming(originalArgs, errorMessage)

      expect(fixedArgs).toEqual({
        userName: "john_doe",
        file_path: "/tmp/test.txt"
      })
      expect(fixedArgs).not.toHaveProperty('user_name')
    })

    it('should not modify args that are already correct', () => {
      const mcpServiceInstance = mcpService as any

      const originalArgs = {
        sessionId: "test-session-123",
        command: "ls ~/Desktop"
      }

      const fixedArgs = mcpServiceInstance.fixParameterNaming(originalArgs)

      expect(fixedArgs).toEqual(originalArgs)
    })

    it('should handle null and undefined args', () => {
      const mcpServiceInstance = mcpService as any

      expect(mcpServiceInstance.fixParameterNaming(null)).toBe(null)
      expect(mcpServiceInstance.fixParameterNaming(undefined)).toBe(undefined)
      expect(mcpServiceInstance.fixParameterNaming({})).toEqual({})
    })
  })

  describe('General MCP Configuration', () => {
    it('should handle empty MCP server configuration', () => {
      const emptyConfig: MCPConfig = {
        mcpServers: {}
      }

      expect(Object.keys(emptyConfig.mcpServers)).toHaveLength(0)
    })

    it('should validate MCP server configuration structure', () => {
      const validConfig: MCPConfig = {
        mcpServers: {
          "test-server": {
            command: "test-command",
            args: ["--arg1", "--arg2"],
            env: { "TEST_VAR": "value" },
            timeout: 30000
          }
        }
      }

      expect(validConfig.mcpServers["test-server"]).toBeDefined()
      expect(validConfig.mcpServers["test-server"].command).toBe("test-command")
      expect(validConfig.mcpServers["test-server"].args).toEqual(["--arg1", "--arg2"])
    })
  })

  describe('Tool Execution Error Recovery', () => {
    it('should detect parameter naming errors', () => {
      const errorMessage = "MCP error -32603: Tool call failed: Invalid request: Invalid arguments: missing field `sessionId`"

      expect(errorMessage.includes("missing field")).toBe(true)
      expect(errorMessage.includes("sessionId")).toBe(true)
    })

    it('should detect general invalid argument errors', () => {
      const errorMessage = "Invalid arguments: expected object with required fields"

      expect(errorMessage.includes("Invalid arguments")).toBe(true)
    })
  })
})
