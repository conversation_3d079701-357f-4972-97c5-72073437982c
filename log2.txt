
> speakmcp@0.0.3 dev /Users/<USER>/Development/whispo
> electron-vite dev --watch

vite v5.4.8 building SSR bundle for development...

watching for file changes...

build started...
transforming...
✓ 383 modules transformed.
rendering chunks...
out/main/index.js             148.60 kB
out/main/updater-l5s32Xwz.js  472.98 kB
built in 749ms.

build the electron main process successfully

-----

vite v5.4.8 building SSR bundle for development...

watching for file changes...

build started...
transforming...
✓ 2 modules transformed.
rendering chunks...
out/preload/index.mjs  2.31 kB
built in 9ms.

build the electron preload files successfully

-----

dev server running for the electron renderer process at:

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose

start electron app...

Skip checkForUpdates because application is not packed and dev update config is not forced
Skip checkForUpdates because application is not packed and dev update config is not forced
